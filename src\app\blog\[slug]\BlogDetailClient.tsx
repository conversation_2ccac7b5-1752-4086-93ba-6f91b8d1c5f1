'use client';

import BlogDetailHeader from '@/components/features/blog/BlogDetailHeader';
import BlogFeaturedImage from '@/components/features/blog/BlogFeaturedImage';
import BlogDetailContent from '@/components/features/blog/BlogDetailContent';
import BlogDetailSidebar from '@/components/features/blog/BlogDetailSidebar';
import { type BlogPost } from '@/lib/api';

interface BlogDetailClientProps {
  post: BlogPost;
  relatedPosts: BlogPost[];
}

export default function BlogDetailClient({ post, relatedPosts }: BlogDetailClientProps) {
  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <BlogDetailHeader post={post} />

      {/* Featured Image */}
      <BlogFeaturedImage post={post} />

      {/* Content */}
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-7xl mx-auto">
          <div className="flex flex-col lg:flex-row gap-12">
            {/* Main Content */}
            <BlogDetailContent post={post} />

            {/* Sidebar */}
            <BlogDetailSidebar relatedPosts={relatedPosts} />
          </div>
        </div>
      </div>
    </div>
  );
}
