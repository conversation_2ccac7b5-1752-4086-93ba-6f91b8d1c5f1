'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import ClientCard from './ClientCard';
import { getFeaturedClients, type Client } from '@/lib/api';

export default function Clients() {
  const [selectedClient, setSelectedClient] = useState<string | null>(null);
  const [clients, setClients] = useState<Client[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchClients = async () => {
      try {
        const featuredClients = await getFeaturedClients();
        setClients(featuredClients);
      } catch (error) {
        console.error('Error fetching clients:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchClients();
  }, []);

  const openClientModal = (clientId: string) => {
    setSelectedClient(clientId);
  };

  const navigateClient = (direction: 'prev' | 'next') => {
    if (selectedClient === null) return;

    const currentIndex = clients.findIndex(client => client._id === selectedClient);
    let newIndex;

    if (direction === 'prev') {
      newIndex = currentIndex > 0 ? currentIndex - 1 : clients.length - 1;
    } else {
      newIndex = currentIndex < clients.length - 1 ? currentIndex + 1 : 0;
    }

    setSelectedClient(clients[newIndex]._id);
  };

  const selectedClientData = clients.find(client => client._id === selectedClient);

  return (
    <section id="clients" className="py-20 bg-background">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-heading font-bold text-primary mb-4">
            Trusted By
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Proud to have collaborated with diverse clients across various industries.
          </p>
        </div>

        {loading ? (
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6 items-stretch">
            {[...Array(12)].map((_, index) => (
              <div key={index} className="h-24 flex items-center justify-center">
                <div className="bg-card p-4 rounded-xl shadow-md w-full h-full flex items-center justify-center min-h-[80px]">
                  <div className="h-12 w-20 bg-gray-200 rounded animate-pulse"></div>
                </div>
              </div>
            ))}
          </div>
        ) : clients.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-lg text-muted-foreground">No clients available at the moment.</p>
          </div>
        ) : (
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6 items-stretch">
            {clients.map((client) => (
              <Dialog key={client._id}>
                <DialogTrigger asChild>
                  <div className="h-24 flex items-center justify-center">
                    <ClientCard
                      client={client}
                      variant="logo"
                      onClick={() => openClientModal(client._id)}
                      className="min-h-[80px]"
                    />
                  </div>
                </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <div className="relative">
                  {/* Navigation Arrows */}
                  <Button
                    variant="ghost"
                    size="icon"
                    className="absolute -left-12 top-1/2 -translate-y-1/2 z-10"
                    onClick={() => navigateClient('prev')}
                  >
                    <ChevronLeft size={20} />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="absolute -right-12 top-1/2 -translate-y-1/2 z-10"
                    onClick={() => navigateClient('next')}
                  >
                    <ChevronRight size={20} />
                  </Button>

                  {selectedClientData && (
                    <ClientCard
                      client={selectedClientData}
                      variant="detailed"
                      showWebsite={true}
                    />
                  )}
                </div>
              </DialogContent>
            </Dialog>
            ))}
          </div>
        )}

        {/* View All Clients Button */}
        <div className="text-center mt-12">
          <Link href="/clients">
            <Button className="bg-primary hover:bg-primary/90 text-primary-foreground px-8 py-3 rounded-full font-semibold transition-all duration-300 hover:scale-105 hover:shadow-lg">
              View All Clients
            </Button>
          </Link>
        </div>

        {/* Stats Section */}
        <div className="mt-20 grid grid-cols-2 md:grid-cols-4 gap-8">
          {[
            { number: '50+', label: 'Projects Completed' },
            { number: '25+', label: 'Happy Clients' },
            { number: '1M+', label: 'Views Generated' },
            { number: '98%', label: 'Client Satisfaction' },
          ].map((stat, index) => (
            <div key={index} className="text-center">
              <div className="text-3xl md:text-4xl font-heading font-bold text-secondary mb-2">
                {stat.number}
              </div>
              <div className="text-muted-foreground font-medium">
                {stat.label}
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
