'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { Quote, Star, ChevronLeft, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { getFeaturedTestimonials, type Testimonial } from '@/lib/api';

export default function Testimonials() {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);
  const [testimonials, setTestimonials] = useState<Testimonial[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchTestimonials = async () => {
      try {
        const featuredTestimonials = await getFeaturedTestimonials();
        setTestimonials(featuredTestimonials);
      } catch (error) {
        console.error('Error fetching testimonials:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchTestimonials();
  }, []);

  useEffect(() => {
    if (!isAutoPlaying || testimonials.length === 0) return;

    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) =>
        prevIndex === testimonials.length - 1 ? 0 : prevIndex + 1
      );
    }, 6000);

    return () => clearInterval(interval);
  }, [isAutoPlaying, testimonials.length]);

  const goToSlide = (index: number) => {
    setCurrentIndex(index);
    setIsAutoPlaying(false);
    setTimeout(() => setIsAutoPlaying(true), 10000); // Resume auto-play after 10 seconds
  };

  const goToPrevious = () => {
    const newIndex = currentIndex === 0 ? testimonials.length - 1 : currentIndex - 1;
    goToSlide(newIndex);
  };

  const goToNext = () => {
    const newIndex = currentIndex === testimonials.length - 1 ? 0 : currentIndex + 1;
    goToSlide(newIndex);
  };

  const currentTestimonial = testimonials[currentIndex];

  if (loading) {
    return (
      <section id="testimonials" className="py-20 bg-background">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-heading font-bold text-primary mb-4">
              What Clients Say
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Feedback from collaborators and clients who trusted me with their vision.
            </p>
          </div>
          <div className="max-w-3xl mx-auto">
            <Card className="bg-card border-border shadow-lg h-[280px] md:h-[240px]">
              <CardContent className="p-6 md:p-8 h-full">
                <div className="flex flex-col md:flex-row items-center md:items-start gap-6 h-full">
                  <div className="flex flex-col items-center md:items-start flex-shrink-0 md:w-32">
                    <div className="w-16 h-16 bg-muted/20 rounded-full animate-pulse mb-3"></div>
                    <div className="h-4 bg-muted/20 rounded animate-pulse w-24 mb-1"></div>
                    <div className="h-3 bg-muted/20 rounded animate-pulse w-20"></div>
                  </div>
                  <div className="flex-1 flex flex-col h-full space-y-3">
                    <div className="h-8 bg-muted/20 rounded animate-pulse w-8 flex-shrink-0"></div>
                    <div className="flex-1 space-y-3">
                      <div className="h-4 bg-muted/20 rounded animate-pulse w-full"></div>
                      <div className="h-4 bg-muted/20 rounded animate-pulse w-4/5"></div>
                      <div className="h-4 bg-muted/20 rounded animate-pulse w-3/4"></div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>
    );
  }

  if (testimonials.length === 0) {
    return (
      <section id="testimonials" className="py-20 bg-background">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-heading font-bold text-primary mb-4">
              What Clients Say
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Feedback from collaborators and clients who trusted me with their vision.
            </p>
          </div>
          <div className="text-center py-12">
            <p className="text-lg text-muted-foreground">No testimonials available at the moment.</p>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section id="testimonials" className="py-20 bg-background">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-heading font-bold text-primary mb-4">
            What Clients Say
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Feedback from collaborators and clients who trusted me with their vision.
          </p>
        </div>

        <div className="max-w-3xl mx-auto">
          <div className="relative">
            {/* Navigation Arrows */}
            <Button
              variant="ghost"
              size="icon"
              className="absolute -left-12 top-1/2 -translate-y-1/2 z-10 text-foreground hover:text-accent hover:bg-accent/10 w-10 h-10"
              onClick={goToPrevious}
            >
              <ChevronLeft size={20} />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="absolute -right-12 top-1/2 -translate-y-1/2 z-10 text-foreground hover:text-accent hover:bg-accent/10 w-10 h-10"
              onClick={goToNext}
            >
              <ChevronRight size={20} />
            </Button>

            {/* Testimonial Card */}
            <Card className="bg-card border-border shadow-lg h-[280px] md:h-[240px]">
              <CardContent className="p-6 md:p-8 h-full">
                <div className="flex flex-col md:flex-row items-center md:items-start gap-6 h-full">
                  {/* Left side - Avatar and Info */}
                  <div className="flex flex-col items-center md:items-start flex-shrink-0 md:w-32">
                    {/* Avatar */}
                    <div className="relative w-16 h-16 rounded-full overflow-hidden border-3 border-accent/30 mb-3">
                      {currentTestimonial.avatar ? (
                        <Image
                          src={currentTestimonial.avatar}
                          alt={currentTestimonial.name}
                          fill
                          className="object-cover"
                        />
                      ) : (
                        <div className="w-full h-full bg-accent flex items-center justify-center text-accent-foreground font-heading font-bold text-lg">
                          {currentTestimonial.name.charAt(0)}
                        </div>
                      )}
                    </div>

                    {/* Author Info */}
                    <div className="text-center md:text-left">
                      <cite className="text-accent font-semibold text-base not-italic block line-clamp-1">
                        {currentTestimonial.name}
                      </cite>
                      <div className="text-muted-foreground text-sm line-clamp-2">
                        {currentTestimonial.role}
                        {currentTestimonial.company && (
                          <span className="block md:inline"> {currentTestimonial.company}</span>
                        )}
                      </div>
                    </div>

                    {/* Rating */}
                    {currentTestimonial.rating && (
                      <div className="flex gap-1 mt-2">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            size={16}
                            className={`${i < currentTestimonial.rating!
                              ? 'text-warning fill-current'
                              : 'text-muted-foreground/30'
                              }`}
                          />
                        ))}
                      </div>
                    )}
                  </div>

                  {/* Right side - Content */}
                  <div className="flex-1 flex flex-col h-full md:min-h-0">
                    {/* Quote Icon */}
                    <Quote size={32} className="text-accent opacity-40 mb-3 flex-shrink-0" />

                    {/* Testimonial Content */}
                    <blockquote className="text-base md:text-lg leading-relaxed text-foreground italic flex-1 overflow-hidden">
                      <div className="line-clamp-4 md:line-clamp-3">
                        "{currentTestimonial.content}"
                      </div>
                    </blockquote>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Dots Indicator */}
          <div className="flex justify-center gap-3 mt-8">
            {testimonials.map((_, index) => (
              <button
                key={index}
                onClick={() => goToSlide(index)}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${index === currentIndex
                  ? 'bg-accent scale-125'
                  : 'bg-muted-foreground/30 hover:bg-muted-foreground/50'
                  }`}
                aria-label={`Go to testimonial ${index + 1}`}
              />
            ))}
          </div>

          {/* Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mt-12 pt-8 border-t border-border/20">
            {[
              { number: '98%', label: 'Client Satisfaction' },
              { number: '50+', label: 'Projects Delivered' },
              { number: '25+', label: 'Happy Clients' },
              { number: '1M+', label: 'Views Generated' },
            ].map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-2xl md:text-3xl font-heading font-bold text-accent mb-1">
                  {stat.number}
                </div>
                <div className="text-muted-foreground font-medium text-xs md:text-sm">
                  {stat.label}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
