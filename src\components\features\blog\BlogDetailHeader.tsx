import Link from 'next/link';
import { ArrowLeft, Calendar, Clock, User, Star } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { formatDate, type BlogPost } from '@/lib/api';

interface BlogDetailHeaderProps {
  post: BlogPost;
}

export default function BlogDetailHeader({ post }: BlogDetailHeaderProps) {
  return (
    <div className="relative bg-background border-b border-border py-20">
      <div className="container mx-auto px-4">
        <Link
          href="/blog"
          className="inline-flex items-center gap-2 text-muted-foreground hover:text-primary transition-colors mb-8 group"
        >
          <ArrowLeft size={20} className="group-hover:-translate-x-1 transition-transform duration-300" />
          Back to Blog
        </Link>

        <div className="max-w-4xl">
          {/* Category and Featured Badges */}
          <div className="flex flex-wrap gap-3 mb-6">
            <div className="flex items-center gap-2 bg-muted px-4 py-2 rounded-full border">
              <span className="text-sm font-medium text-foreground">📝 {post.category}</span>
            </div>
            {post.featured && (
              <div className="flex items-center gap-2 bg-accent px-4 py-2 rounded-full border border-accent">
                <Star size={14} className="text-accent-foreground" />
                <span className="text-sm font-semibold text-accent-foreground">Featured</span>
              </div>
            )}
          </div>

          {/* Title */}
          <h1 className="text-3xl md:text-4xl lg:text-5xl font-heading font-bold mb-6 leading-tight text-foreground">
            {post.title}
          </h1>

          {/* Meta Information */}
          <div className="flex flex-wrap items-center gap-4 text-muted-foreground mb-6">
            <div className="flex items-center gap-2 bg-muted px-3 py-2 rounded-lg border">
              <Calendar size={16} />
              <span className="text-sm">{formatDate(post.createdAt)}</span>
            </div>
            <div className="flex items-center gap-2 bg-muted px-3 py-2 rounded-lg border">
              <Clock size={16} />
              <span className="text-sm">{post.readTime} min read</span>
            </div>
            <div className="flex items-center gap-2 bg-muted px-3 py-2 rounded-lg border">
              <User size={16} />
              <span className="text-sm">Uttam Rimal</span>
            </div>
          </div>

          {/* Excerpt */}
          <p className="text-xl md:text-2xl text-muted-foreground max-w-3xl leading-relaxed">
            {post.excerpt}
          </p>

          {/* Tags Preview */}
          <div className="flex flex-wrap gap-2 mt-8">
            {post.tags.slice(0, 4).map((tag) => (
              <div key={tag} className="bg-secondary/10 border border-secondary/20 px-3 py-1 rounded-full">
                <span className="text-sm text-secondary">#{tag}</span>
              </div>
            ))}
            {post.tags.length > 4 && (
              <div className="bg-secondary/10 border border-secondary/20 px-3 py-1 rounded-full">
                <span className="text-sm text-secondary">+{post.tags.length - 4} more</span>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
